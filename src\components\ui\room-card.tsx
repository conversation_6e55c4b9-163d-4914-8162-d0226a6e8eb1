"use client";


import Image from 'next/image';
import { Heart, MapPin, Wifi, Zap, Droplets, Users } from 'lucide-react';

interface RoomListing {
  id: string;
  slug: string;
  title: string;
  description: string;
  monthlyRent: number;
  area: number;
  maxOccupancy: number;
  roomType: string;
  isVerified: boolean;
  images: string[];
  amenities: Array<{
    id: string;
    name: string;
    category: string;
  }>;
  costTypes: Array<{
    id: string;
    name: string;
    amount: number;
    category: string;
    unit: string;
  }>;
  address: {
    street: string;
    ward: string;
    district: string;
    province: string;
  };
  landlord: {
    id: string;
    firstName: string;
    lastName: string;
    phone: string;
    avatar?: string;
    isVerified: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

interface RoomCardProps {
  room: RoomListing;
  onSaveToggle?: (roomId: string) => void;
  isSaved?: boolean;
  onClick?: (slug: string) => void;
  className?: string;
}

export function RoomCard({ 
  room, 
  onSaveToggle, 
  isSaved = false, 
  onClick,
  className = '' 
}: RoomCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN').format(price / 1000000);
  };

  const getElectricityWaterCost = () => {
    // Add defensive check for costTypes
    if (!room.costTypes || !Array.isArray(room.costTypes)) {
      return { electricityCost: undefined, waterCost: undefined };
    }

    const electricityCost = room.costTypes.find(cost =>
      cost.category === 'utility' && cost.name.toLowerCase().includes('điện')
    );
    const waterCost = room.costTypes.find(cost =>
      cost.category === 'utility' && cost.name.toLowerCase().includes('nước')
    );

    return { electricityCost, waterCost };
  };

  const hasWifi = () => {
    // Add defensive check for amenities
    if (!room.amenities || !Array.isArray(room.amenities)) {
      return false;
    }

    return room.amenities.some(amenity =>
      amenity.name.toLowerCase().includes('wifi') ||
      amenity.name.toLowerCase().includes('internet')
    );
  };

  const handleClick = () => {
    if (onClick) {
      onClick(room.slug);
    }
  };

  const handleSaveClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSaveToggle) {
      onSaveToggle(room.id);
    }
  };

  const { electricityCost, waterCost } = getElectricityWaterCost();
  const wifiAvailable = hasWifi();

  return (
    <div 
      className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer ${className}`}
      onClick={handleClick}
    >
      {/* Image Container */}
      <div className="relative h-48">
        <Image
          src={room.images?.[0] && room.images[0].trim() !== "" ? room.images[0] : "/placeholder-room.jpg"}
          alt={room.title || "Room image"}
          fill
          className="object-cover"
        />
        
        {/* Verified Badge */}
        {room.isVerified && (
          <div className="absolute top-2 left-2">
            <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded">
              ĐÃ XÁC MINH
            </span>
          </div>
        )}

        {/* WiFi Icon */}
        {wifiAvailable && (
          <div className="absolute top-2 right-12">
            <div className="bg-white/90 rounded-full p-1">
              <Wifi className="h-4 w-4 text-green-600" />
            </div>
          </div>
        )}

        {/* Save Button */}
        {onSaveToggle && (
          <button
            onClick={handleSaveClick}
            className="absolute top-2 right-2 p-2 bg-white/80 rounded-full hover:bg-white transition-colors"
          >
            <Heart 
              className={`h-4 w-4 ${isSaved ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} 
            />
          </button>
        )}
      </div>

      {/* Content */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
          {room.title}
        </h3>

        {/* Price */}
        <div className="text-red-600 font-bold text-lg mb-2">
          {formatPrice(room.monthlyRent)} triệu/tháng
        </div>

        {/* Room Type & Area */}
        <div className="text-sm text-gray-600 mb-2">
          {room.roomType} • {room.area}m²
          {room.maxOccupancy > 1 && (
            <>
              {' • '}
              <span className="inline-flex items-center gap-1">
                <Users className="h-3 w-3" />
                {room.maxOccupancy} người
              </span>
            </>
          )}
        </div>

        {/* Electricity & Water Costs */}
        {(electricityCost || waterCost) && (
          <div className="flex items-center gap-3 text-xs text-gray-600 mb-2">
            {electricityCost && (
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                <span>{new Intl.NumberFormat('vi-VN').format(electricityCost.amount)}đ/{electricityCost.unit}</span>
              </div>
            )}
            {waterCost && (
              <div className="flex items-center gap-1">
                <Droplets className="h-3 w-3" />
                <span>{new Intl.NumberFormat('vi-VN').format(waterCost.amount)}đ/{waterCost.unit}</span>
              </div>
            )}
          </div>
        )}

        {/* Location */}
        <div className="flex items-center text-sm text-gray-600">
          <MapPin className="h-4 w-4 mr-1" />
          <span>{room.address.district}, {room.address.province}</span>
        </div>
      </div>
    </div>
  );
}
